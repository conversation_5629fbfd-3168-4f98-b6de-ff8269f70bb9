# Nacos配置中心集成指南

## 概述

本文档介绍如何在alpha-service项目中使用Nacos配置中心进行动态配置管理。

## 功能特性

- ✅ **单例模式**：确保全局唯一的配置管理实例
- ✅ **多环境支持**：支持daily、pre、prod等不同环境配置
- ✅ **配置热更新**：支持配置变更的实时监听和自动更新
- ✅ **线程安全**：提供线程安全的配置获取和更新机制
- ✅ **降级处理**：当Nacos不可用时，自动降级到本地缓存配置
- ✅ **多格式支持**：支持JSON和YAML格式的配置文件
- ✅ **嵌套配置访问**：支持点分隔的嵌套配置键访问

## 配置方法

### 1. 环境配置

在 `properties.toml` 文件中为每个环境配置Nacos连接信息：

```toml
# 默认配置
[default]
nacos_endpoint = "http://jmenv.cn-hangzhou.aliyun-inc.com:8080/diamond-server/diamond"
nacos_data_id = "wuying-alpha-service:application"
nacos_group = "DEFAULT_GROUP"

# Daily环境
[daily]
nacos_endpoint = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
nacos_data_id = "wuying-alpha-service:application-daily"
nacos_group = "DEFAULT_GROUP"

# Pre环境
[pre]
nacos_endpoint = "http://pre-diamond.internal/diamond-server/diamond"
nacos_data_id = "wuying-alpha-service:application"
nacos_group = "DEFAULT_GROUP"

# Prod环境
[prod]
nacos_endpoint = "http://diamond.internal/diamond-server/diamond"
nacos_data_id = "wuying-alpha-service:application"
nacos_group = "DEFAULT_GROUP"
```

### 2. 依赖安装

确保安装了必要的依赖包：

```bash
pip install nacos-sdk-python==0.1.16 pyyaml
```

## API使用示例

### 基本使用

```python
from shared.config.nacos_config import nacos_config_manager

# 获取完整配置
config = nacos_config_manager.get_config()
print(f"完整配置: {config}")

# 获取特定配置值
database_host = nacos_config_manager.get_config_value("database.host", "localhost")
redis_port = nacos_config_manager.get_config_value("redis.port", 6379)

# 获取嵌套配置
api_config = nacos_config_manager.get_config_value("api.settings", {})
```

### 指定配置源

```python
# 从特定的data_id和group获取配置
custom_config = nacos_config_manager.get_config(
    data_id="custom-service:config",
    group="CUSTOM_GROUP"
)

# 获取特定配置源的值
custom_value = nacos_config_manager.get_config_value(
    key="feature.enabled",
    default=False,
    data_id="feature-flags:config",
    group="FEATURE_GROUP"
)
```

### 配置热更新监听

```python
def on_config_changed(new_config):
    """配置变更回调函数"""
    print(f"配置已更新: {new_config}")
    # 在这里处理配置变更逻辑
    # 例如：重新初始化数据库连接、更新缓存等

# 添加配置监听器
nacos_config_manager.add_config_listener(on_config_changed)

# 为特定配置添加监听器
nacos_config_manager.add_config_listener(
    callback=on_config_changed,
    data_id="database:config",
    group="DB_GROUP"
)
```

### 手动刷新配置

```python
# 刷新默认配置
success = nacos_config_manager.refresh_config()
if success:
    print("配置刷新成功")

# 刷新特定配置
success = nacos_config_manager.refresh_config(
    data_id="cache:config",
    group="CACHE_GROUP"
)
```

### 检查连接状态

```python
# 检查Nacos连接状态
if nacos_config_manager.is_connected():
    print("Nacos连接正常")
else:
    print("Nacos连接异常，使用降级配置")

# 获取详细连接信息
info = nacos_config_manager.get_connection_info()
print(f"连接信息: {info}")
```

### 设置降级配置

```python
# 设置降级配置，当Nacos不可用时使用
fallback_config = {
    "database": {
        "host": "localhost",
        "port": 3306,
        "name": "alpha_service"
    },
    "redis": {
        "host": "localhost",
        "port": 6379
    }
}

nacos_config_manager.set_fallback_config(fallback_config)
```

## 错误处理说明

### 1. 网络异常处理

当Nacos服务不可用时，系统会自动：
- 使用本地缓存的配置
- 记录错误日志
- 尝试使用降级配置

```python
# 示例：安全获取配置
try:
    config = nacos_config_manager.get_config()
    if not config:
        # 使用默认配置
        config = get_default_config()
except Exception as e:
    logger.error(f"获取Nacos配置失败: {e}")
    config = get_default_config()
```

### 2. 配置解析异常

支持多种配置格式，解析失败时会：
- 尝试不同的解析器（JSON -> YAML）
- 返回原始字符串内容
- 记录警告日志

### 3. 超时处理

所有Nacos操作都设置了合理的超时时间：
- 配置获取：5秒超时
- 连接测试：3秒超时

## 最佳实践建议

### 1. 配置结构设计

建议使用层次化的配置结构：

```yaml
# 推荐的配置结构
database:
  host: "localhost"
  port: 3306
  name: "alpha_service"
  pool:
    min_size: 5
    max_size: 20

redis:
  host: "localhost"
  port: 6379
  db: 0
  timeout: 5

features:
  new_algorithm: true
  debug_mode: false
```

### 2. 配置监听最佳实践

```python
class ConfigurableService:
    def __init__(self):
        self.config = {}
        self._load_config()
        # 添加配置监听
        nacos_config_manager.add_config_listener(self._on_config_changed)
    
    def _load_config(self):
        """加载配置"""
        self.config = nacos_config_manager.get_config_value("service.settings", {})
    
    def _on_config_changed(self, new_config):
        """配置变更处理"""
        old_config = self.config.copy()
        self._load_config()
        
        # 只在配置真正变化时处理
        if old_config != self.config:
            self._handle_config_change(old_config, self.config)
    
    def _handle_config_change(self, old_config, new_config):
        """处理配置变更"""
        # 实现具体的配置变更逻辑
        pass
```

### 3. 错误处理和降级

```python
class RobustConfigService:
    def __init__(self):
        # 设置默认降级配置
        self._set_fallback_configs()
    
    def _set_fallback_configs(self):
        """设置降级配置"""
        fallback = {
            "database": {"host": "localhost", "port": 3306},
            "redis": {"host": "localhost", "port": 6379}
        }
        nacos_config_manager.set_fallback_config(fallback)
    
    def get_database_config(self):
        """获取数据库配置"""
        return nacos_config_manager.get_config_value(
            "database", 
            {"host": "localhost", "port": 3306}  # 默认值
        )
```

### 4. 性能优化

- 配置会自动缓存，避免频繁的网络请求
- 使用配置监听而不是轮询检查配置变更
- 合理设置降级配置，确保服务可用性

## 注意事项

1. **单例模式**：NacosConfigManager使用单例模式，确保全局只有一个实例
2. **线程安全**：所有配置操作都是线程安全的
3. **配置格式**：支持JSON和YAML格式，建议使用YAML提高可读性
4. **网络依赖**：首次启动时需要网络连接到Nacos，后续可以使用缓存配置
5. **配置变更**：配置变更会触发所有相关监听器，注意避免循环依赖

## 故障排查

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证endpoint配置是否正确
   - 查看防火墙设置

2. **配置获取失败**
   - 检查data_id和group是否正确
   - 验证Nacos中是否存在对应配置
   - 查看权限设置

3. **配置解析失败**
   - 检查配置内容格式是否正确
   - 验证JSON/YAML语法
   - 查看日志中的详细错误信息

### 日志查看

相关日志会记录在应用日志中，搜索关键字：
- `[NacosConfig]` - Nacos相关日志
- `连接Nacos` - 连接状态日志
- `配置加载` - 配置加载日志
- `配置已更新` - 配置变更日志
