#!/usr/bin/env python3
"""
测试脚本：使用Diamond协议获取aippt_config配置
针对Diamond配置中心的HTTP API进行测试
"""

import sys
import os
import json
import time
import requests
from pathlib import Path
from urllib.parse import urlencode

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_diamond_config_direct():
    """直接使用Diamond HTTP API获取配置"""
    print("=== 使用Diamond HTTP API获取配置 ===")
    
    try:
        from shared.config.environments import env_manager
        
        # 获取环境配置
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint  # 实际上是Diamond endpoint
        
        print(f"📡 Diamond端点: {endpoint}")
        
        # Diamond API参数
        target_data_id = "wuying-alpha-service:aippt_config"
        target_group = "DEFAULT_GROUP"
        
        # 构建Diamond获取配置的URL
        params = {
            'dataId': target_data_id,
            'group': target_group
        }
        
        config_url = f"{endpoint}?{urlencode(params)}"
        print(f"🔗 请求URL: {config_url}")
        
        # 发送HTTP GET请求
        print(f"🔍 正在获取配置...")
        
        headers = {
            'User-Agent': 'Diamond-Client/1.0',
            'Accept': 'text/plain,application/json,*/*'
        }
        
        response = requests.get(config_url, headers=headers, timeout=15)
        
        print(f"📊 响应状态: HTTP {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            config_content = response.text
            print(f"✅ 成功获取配置内容 ({len(config_content)} 字符)")
            
            if config_content.strip():
                print("📄 配置内容:")
                print("=" * 60)
                print(config_content)
                print("=" * 60)
                
                # 尝试解析配置内容
                try:
                    parsed_config = json.loads(config_content)
                    print("✅ 配置内容为有效JSON格式")
                    print("📋 格式化配置:")
                    formatted = json.dumps(parsed_config, indent=2, ensure_ascii=False)
                    print(formatted)
                    return parsed_config
                except json.JSONDecodeError:
                    try:
                        import yaml
                        parsed_config = yaml.safe_load(config_content)
                        print("✅ 配置内容为有效YAML格式")
                        return parsed_config
                    except:
                        print("⚠️ 配置内容为纯文本格式")
                        return {"raw_content": config_content}
            else:
                print("❌ 配置内容为空")
                return None
                
        elif response.status_code == 404:
            print("❌ 配置不存在 (HTTP 404)")
            return None
        else:
            print(f"❌ 获取配置失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，无法连接到Diamond服务器")
        return None
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_diamond_config_list():
    """尝试列出Diamond中的配置"""
    print("\n=== 尝试列出Diamond中的配置 ===")
    
    try:
        from shared.config.environments import env_manager
        
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint
        
        # 尝试不同的API端点来列出配置
        possible_list_apis = [
            "/diamond-server/config.co",
            "/diamond-server/admin.do",
            "/diamond-server/list.do",
            "/diamond-server/basestone.do?method=getAllConfigByTenant",
        ]
        
        base_url = endpoint.replace("/diamond-server/diamond", "")
        
        for api_path in possible_list_apis:
            try:
                list_url = f"{base_url}{api_path}"
                print(f"🔍 尝试: {list_url}")
                
                response = requests.get(list_url, timeout=10)
                print(f"  状态: HTTP {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text[:500]  # 只显示前500字符
                    print(f"  内容预览: {content}...")
                    
            except Exception as e:
                print(f"  错误: {e}")
                
    except Exception as e:
        print(f"❌ 列出配置失败: {e}")


def test_diamond_default_config():
    """测试获取默认配置"""
    print("\n=== 测试获取默认配置 ===")
    
    try:
        from shared.config.environments import env_manager
        
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint
        default_data_id = config.nacos_data_id
        default_group = config.nacos_group
        
        print(f"🎯 默认配置: {default_group}:{default_data_id}")
        
        # 构建请求参数
        params = {
            'dataId': default_data_id,
            'group': default_group
        }
        
        config_url = f"{endpoint}?{urlencode(params)}"
        print(f"🔗 请求URL: {config_url}")
        
        headers = {
            'User-Agent': 'Diamond-Client/1.0',
            'Accept': 'text/plain,application/json,*/*'
        }
        
        response = requests.get(config_url, headers=headers, timeout=15)
        
        print(f"📊 响应状态: HTTP {response.status_code}")
        
        if response.status_code == 200:
            config_content = response.text
            print(f"✅ 成功获取默认配置 ({len(config_content)} 字符)")
            
            if config_content.strip():
                # 只显示前200字符的预览
                preview = config_content[:200] + "..." if len(config_content) > 200 else config_content
                print(f"📄 配置预览:\n{preview}")
                return True
            else:
                print("❌ 默认配置内容为空")
                return False
        else:
            print(f"❌ 获取默认配置失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试默认配置失败: {e}")
        return False


def test_create_diamond_config():
    """尝试创建Diamond配置"""
    print("\n=== 尝试创建Diamond配置 ===")
    
    try:
        from shared.config.environments import env_manager
        
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint
        
        # 构建发布配置的URL（通常是POST请求）
        publish_url = endpoint.replace("/diamond", "/basestone.do")
        
        target_data_id = "wuying-alpha-service:aippt_config"
        target_group = "DEFAULT_GROUP"
        
        # 测试配置内容
        test_config = {
            "aippt": {
                "enabled": True,
                "version": "1.0.0",
                "features": {
                    "auto_generate": True,
                    "template_support": True,
                    "export_formats": ["pptx", "pdf"]
                }
            },
            "service": {
                "name": "alpha-service",
                "version": "1.0.0"
            },
            "created_by": "test_script",
            "created_at": time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        config_content = json.dumps(test_config, indent=2, ensure_ascii=False)
        
        # 构建POST数据
        post_data = {
            'method': 'addConfig',
            'dataId': target_data_id,
            'group': target_group,
            'content': config_content
        }
        
        print(f"📝 尝试创建配置: {target_group}:{target_data_id}")
        print(f"🔗 发布URL: {publish_url}")
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Diamond-Client/1.0'
        }
        
        response = requests.post(publish_url, data=post_data, headers=headers, timeout=15)
        
        print(f"📊 响应状态: HTTP {response.status_code}")
        print(f"📋 响应内容: {response.text}")
        
        if response.status_code == 200 and "success" in response.text.lower():
            print("✅ 配置创建可能成功")
            
            # 等待一下然后验证
            time.sleep(2)
            created_config = test_diamond_config_direct()
            if created_config:
                print("✅ 配置创建验证成功")
                return True
            else:
                print("❌ 配置创建验证失败")
                return False
        else:
            print("❌ 配置创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建配置失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始测试Diamond配置获取")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示环境信息
    try:
        from shared.config.environments import env_manager
        env_info = env_manager.get_environment_info()
        print(f"🌍 当前环境: {env_info['current_environment']}")
    except:
        print("⚠️ 无法获取环境信息")
    
    # 测试1: 获取默认配置验证连接
    print("\n" + "="*50)
    default_success = test_diamond_default_config()
    
    # 测试2: 直接获取目标配置
    print("\n" + "="*50)
    config = test_diamond_config_direct()
    
    # 测试3: 如果配置不存在，尝试创建
    if not config:
        print("\n" + "="*50)
        print("💡 目标配置不存在，尝试创建...")
        created = test_create_diamond_config()
        
        if created:
            print("✅ 配置创建成功，重新获取...")
            config = test_diamond_config_direct()
    
    # 测试4: 尝试列出配置
    print("\n" + "="*50)
    test_diamond_config_list()
    
    print(f"\n🏁 所有测试完成")
    
    if config:
        print("✅ 成功获取到配置")
        print(f"📋 配置摘要: {len(config)} 个配置项")
        return True
    else:
        print("❌ 未能获取到配置")
        print("\n📋 总结:")
        print("  1. 检查Diamond服务器是否正常运行")
        print("  2. 确认配置 'wuying-alpha-service:aippt_config' 是否存在")
        print("  3. 可能需要在Diamond控制台手动创建配置")
        print("  4. 检查网络连接和API权限")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
