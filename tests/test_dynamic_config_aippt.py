#!/usr/bin/env python3
"""
使用DynamicConfig获取wuying-alpha-service:aippt_config配置
扩展DynamicConfig类以支持获取任意dataId的配置
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


class ExtendedDynamicConfig:
    """扩展的DynamicConfig类，支持获取任意dataId的配置，使用HTTP方式"""

    def __init__(self):
        # 延迟导入以避免循环依赖
        from shared.config.environments import env_manager
        env_config = env_manager.get_config()

        self.endpoint = env_config.diamond_endpoint
        self.default_data_id = env_config.diamond_data_id

        # 如果endpoint无法连接，使用备用endpoint
        if "pre-jmenv.cn-hangzhou.aliyun-inc.com" in self.endpoint:
            self.endpoint = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
            print(f"⚠️ 使用备用endpoint: {self.endpoint}")

        self._init_client()

    def _init_client(self):
        """初始化HTTP客户端"""
        try:
            import requests
            # 测试连接
            test_response = requests.get(self.endpoint, timeout=5)
            print(f"✅ Diamond配置中心连接成功: {self.endpoint}")
            self.http_available = True
        except ImportError:
            print("❌ 警告: requests库未安装，配置中心功能不可用")
            self.http_available = False
        except Exception as e:
            print(f"❌ Diamond配置中心连接失败: {e}")
            self.http_available = False

    def get_config(self, data_id: str = None, group: str = "DEFAULT_GROUP") -> str:
        """
        获取指定配置（使用HTTP方式）

        Args:
            data_id: 配置ID，如果为None则使用默认配置
            group: 配置组，默认为DEFAULT_GROUP

        Returns:
            str: 配置内容
        """
        if not self.http_available:
            print("❌ HTTP客户端不可用")
            return None

        if data_id is None:
            data_id = self.default_data_id

        try:
            import requests
            from urllib.parse import urlencode

            print(f"🔍 正在获取配置: {group}:{data_id}")

            # 构建请求参数
            params = {
                'dataId': data_id,
                'group': group
            }

            config_url = f"{self.endpoint}?{urlencode(params)}"

            # 发送HTTP请求
            headers = {
                'User-Agent': 'Diamond-Client/1.0',
                'Accept': 'text/plain,application/json,*/*'
            }

            response = requests.get(config_url, headers=headers, timeout=15)

            if response.status_code == 200:
                config_content = response.text
                if config_content.strip():
                    print(f"✅ 成功获取配置 ({len(config_content)} 字符)")
                    return config_content
                else:
                    print(f"❌ 配置内容为空: {group}:{data_id}")
                    return None
            else:
                print(f"❌ 获取配置失败: HTTP {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取配置失败: {e}")
            return None

    def get_config_by_key(self, key: str, config_content: str = None) -> str:
        """
        根据key获取配置值
        
        Args:
            key: 配置键
            config_content: 配置内容，如果为None则使用最后获取的配置
            
        Returns:
            str: 配置值
        """
        if config_content is None:
            print("❌ 没有提供配置内容")
            return None
        
        try:
            config_dict = json.loads(config_content)
            return config_dict.get(key)
        except json.JSONDecodeError:
            print("⚠️ 配置格式不是有效的JSON，返回原始内容")
            return config_content
        except Exception as e:
            print(f"❌ 解析配置失败: {e}")
            return None

    def add_config_listener(self, data_id: str, group: str, callback):
        """
        添加配置监听器（HTTP方式暂不支持实时监听）

        Args:
            data_id: 配置ID
            group: 配置组
            callback: 回调函数
        """
        print(f"⚠️ HTTP方式暂不支持实时配置监听: {group}:{data_id}")
        print("💡 建议使用定时轮询方式检查配置变更")

    def poll_config_changes(self, data_id: str, group: str, callback, interval: int = 30):
        """
        轮询检查配置变更

        Args:
            data_id: 配置ID
            group: 配置组
            callback: 回调函数
            interval: 轮询间隔（秒）
        """
        import threading
        import time

        last_config = self.get_config(data_id, group)

        def poll_worker():
            nonlocal last_config
            while True:
                try:
                    time.sleep(interval)
                    current_config = self.get_config(data_id, group)

                    if current_config != last_config:
                        print(f"🔔 检测到配置变更: {group}:{data_id}")
                        callback({'content': current_config})
                        last_config = current_config

                except Exception as e:
                    print(f"❌ 轮询配置变更失败: {e}")

        # 启动后台轮询线程
        poll_thread = threading.Thread(target=poll_worker, daemon=True)
        poll_thread.start()
        print(f"✅ 配置轮询监听器启动成功: {group}:{data_id} (间隔: {interval}秒)")


def test_get_aippt_config():
    """测试获取AIPPT配置"""
    print("=== 使用ExtendedDynamicConfig获取AIPPT配置 ===")
    
    # 创建扩展的配置客户端
    config_client = ExtendedDynamicConfig()
    
    # 目标配置参数
    target_data_id = "wuying-alpha-service:aippt_config"
    target_group = "DEFAULT_GROUP"
    
    print(f"\n🎯 目标配置:")
    print(f"  - DataId: {target_data_id}")
    print(f"  - Group: {target_group}")
    
    # 获取配置
    config_content = config_client.get_config(
        data_id=target_data_id,
        group=target_group
    )
    
    if config_content:
        print(f"\n📄 配置内容:")
        print("=" * 60)
        print(config_content)
        print("=" * 60)
        
        # 解析配置内容
        parse_aippt_config(config_content)
        
        # 保存配置
        save_config_to_file(config_content, "aippt_config_dynamic.txt")
        
        return config_content
    else:
        print("❌ 未能获取到配置")
        return None


def test_get_default_config():
    """测试获取默认配置"""
    print("\n=== 测试获取默认配置 ===")
    
    config_client = ExtendedDynamicConfig()
    
    # 获取默认配置
    default_config = config_client.get_config()
    
    if default_config:
        print(f"✅ 默认配置获取成功 ({len(default_config)} 字符)")
        # 只显示前200字符的预览
        preview = default_config[:200] + "..." if len(default_config) > 200 else default_config
        print(f"📄 默认配置预览:\n{preview}")
        return True
    else:
        print("❌ 默认配置获取失败")
        return False


def test_config_listener():
    """测试配置监听器"""
    print("\n=== 测试配置监听器 ===")
    
    config_client = ExtendedDynamicConfig()
    
    # 配置变更计数器
    change_count = 0
    
    def on_config_changed(args):
        """配置变更回调函数"""
        nonlocal change_count
        change_count += 1
        print(f"🔔 配置变更通知 #{change_count}")
        print(f"  - 内容: {args.get('content', '')[:100]}...")
    
    # 添加监听器
    target_data_id = "wuying-alpha-service:aippt_config"
    target_group = "DEFAULT_GROUP"
    
    config_client.add_config_listener(
        data_id=target_data_id,
        group=target_group,
        callback=on_config_changed
    )
    
    print("⏳ 等待配置变更通知（5秒）...")
    time.sleep(5)
    
    if change_count > 0:
        print(f"✅ 检测到 {change_count} 次配置变更")
    else:
        print("ℹ️ 未检测到配置变更（正常情况）")


def parse_aippt_config(config_content):
    """解析AIPPT配置内容"""
    if not config_content:
        return None
    
    # 尝试解析为JSON
    try:
        config_dict = json.loads(config_content)
        print(f"\n📋 配置解析结果（JSON格式）:")
        print(f"  - 配置项数量: {len(config_dict)}")
        for key, value in list(config_dict.items())[:5]:  # 只显示前5个
            print(f"  - {key}: {str(value)[:50]}...")
        if len(config_dict) > 5:
            print(f"  - ... 还有 {len(config_dict) - 5} 个配置项")
        return config_dict
    except json.JSONDecodeError:
        pass
    
    # 如果不是JSON，尝试按行解析（IP列表）
    lines = [line.strip() for line in config_content.strip().split('\n') if line.strip()]
    
    if all(line.replace('.', '').replace(':', '').isdigit() or 
           all(part.isdigit() for part in line.split('.')) for line in lines[:5]):
        print(f"\n📋 配置解析结果（IP地址列表）:")
        print(f"  - 配置类型: IP地址列表")
        print(f"  - IP地址数量: {len(lines)}")
        print(f"  - 前10个IP地址:")
        
        for i, ip in enumerate(lines[:10]):
            print(f"    {i+1:2d}. {ip}")
        
        if len(lines) > 10:
            print(f"    ... 还有 {len(lines) - 10} 个IP地址")
        
        return {"type": "ip_list", "count": len(lines), "ips": lines}
    
    # 其他格式
    print(f"\n📋 配置解析结果（文本格式）:")
    print(f"  - 配置类型: 纯文本")
    print(f"  - 内容长度: {len(config_content)} 字符")
    print(f"  - 行数: {len(lines)}")
    
    return {"type": "text", "content": config_content, "lines": len(lines)}


def save_config_to_file(config_content, filename):
    """保存配置到文件"""
    try:
        output_file = Path(__file__).parent / filename
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"\n💾 配置已保存到: {output_file}")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 使用DynamicConfig获取AIPPT配置")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示环境信息
    try:
        from shared.config.environments import env_manager
        env_info = env_manager.get_environment_info()
        print(f"🌍 当前环境: {env_info['current_environment']}")
    except:
        print("⚠️ 无法获取环境信息")
    
    print("-" * 60)
    
    # 测试1: 获取默认配置（验证连接）
    default_success = test_get_default_config()
    
    print("-" * 60)
    
    # 测试2: 获取AIPPT配置
    config = test_get_aippt_config()
    
    # 测试3: 配置监听器
    if config:
        test_config_listener()
    
    print("-" * 60)
    print("🏁 测试完成")
    
    if config:
        print("✅ 成功获取到AIPPT配置")
        return True
    else:
        print("❌ 未能获取到AIPPT配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
