#!/usr/bin/env python3
"""
使用扩展的DynamicConfig获取wuying-alpha-service:aippt_config配置
基于HTTP方式实现，兼容Diamond配置中心
"""

import sys
import os
import json
import time
import requests
from pathlib import Path
from urllib.parse import urlencode

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


class HttpDynamicConfig:
    """基于HTTP的DynamicConfig实现"""
    
    def __init__(self):
        from shared.config.environments import env_manager
        env_config = env_manager.get_config()
        
        self.endpoint = env_config.diamond_endpoint
        self.default_data_id = env_config.diamond_data_id
        
        # 处理endpoint兼容性
        if "pre-jmenv.cn-hangzhou.aliyun-inc.com" in self.endpoint:
            self.endpoint = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
            print(f"⚠️ 使用备用endpoint: {self.endpoint}")
        
        print(f"🔗 Diamond配置中心: {self.endpoint}")

    def get_config(self, data_id: str = None, group: str = "DEFAULT_GROUP") -> str:
        """
        获取配置
        
        Args:
            data_id: 配置ID
            group: 配置组
            
        Returns:
            str: 配置内容
        """
        if data_id is None:
            data_id = self.default_data_id
        
        try:
            # 构建请求参数
            params = {
                'dataId': data_id,
                'group': group
            }
            
            config_url = f"{self.endpoint}?{urlencode(params)}"
            
            # 发送HTTP请求
            headers = {
                'User-Agent': 'Diamond-Client/1.0',
                'Accept': 'text/plain,application/json,*/*'
            }
            
            print(f"🔍 获取配置: {group}:{data_id}")
            
            response = requests.get(config_url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                config_content = response.text
                if config_content.strip():
                    print(f"✅ 获取成功 ({len(config_content)} 字符)")
                    return config_content
                else:
                    print(f"❌ 配置内容为空")
                    return None
            else:
                print(f"❌ 获取失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取配置异常: {e}")
            return None

    def get_config_by_key(self, key: str, config_content: str) -> str:
        """从配置内容中获取指定键的值"""
        try:
            config_dict = json.loads(config_content)
            return config_dict.get(key)
        except json.JSONDecodeError:
            print("⚠️ 配置不是JSON格式")
            return None


def get_aippt_config():
    """获取AIPPT配置的主函数"""
    print("🚀 使用HttpDynamicConfig获取AIPPT配置")
    print(f"📅 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示环境信息
    try:
        from shared.config.environments import env_manager
        env_info = env_manager.get_environment_info()
        print(f"🌍 环境: {env_info['current_environment']}")
    except:
        print("⚠️ 无法获取环境信息")
    
    print("-" * 50)
    
    # 创建配置客户端
    config_client = HttpDynamicConfig()
    
    # 获取AIPPT配置
    target_data_id = "wuying-alpha-service:aippt_config"
    target_group = "DEFAULT_GROUP"
    
    config_content = config_client.get_config(
        data_id=target_data_id,
        group=target_group
    )
    
    if config_content:
        print("-" * 50)
        print("📄 配置内容:")
        print(config_content)
        print("-" * 50)
        
        # 解析配置
        parsed_data = parse_config_content(config_content)
        
        # 保存配置
        save_config_files(config_content, parsed_data)
        
        print("-" * 50)
        print("✅ 配置获取完成")
        
        # 显示摘要
        print(f"\n📊 配置摘要:")
        print(f"  - 配置名称: {target_data_id}")
        print(f"  - 配置组: {target_group}")
        if parsed_data:
            print(f"  - 配置类型: {parsed_data.get('type', '未知')}")
            if parsed_data.get('type') == 'ip_list':
                print(f"  - IP数量: {parsed_data.get('count', 0)}")
        print(f"  - 内容长度: {len(config_content)} 字符")
        
        return config_content
    else:
        print("❌ 未能获取到配置")
        return None


def parse_config_content(config_content):
    """解析配置内容"""
    if not config_content:
        return None
    
    # 尝试解析为JSON
    try:
        config_dict = json.loads(config_content)
        return {
            "type": "json",
            "data": config_dict,
            "count": len(config_dict)
        }
    except json.JSONDecodeError:
        pass
    
    # 尝试解析为IP列表
    lines = [line.strip() for line in config_content.strip().split('\n') if line.strip()]
    
    # 检查是否为IP地址列表
    if lines and all(is_ip_address(line) for line in lines[:5]):
        return {
            "type": "ip_list",
            "ips": lines,
            "count": len(lines)
        }
    
    # 其他格式
    return {
        "type": "text",
        "content": config_content,
        "lines": len(lines)
    }


def is_ip_address(text):
    """简单检查是否为IP地址格式"""
    parts = text.split('.')
    if len(parts) != 4:
        return False
    
    try:
        for part in parts:
            num = int(part)
            if not (0 <= num <= 255):
                return False
        return True
    except ValueError:
        return False


def save_config_files(config_content, parsed_data):
    """保存配置到文件"""
    try:
        base_path = Path(__file__).parent
        
        # 保存原始配置
        raw_file = base_path / "aippt_config_raw.txt"
        with open(raw_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print(f"💾 原始配置已保存: {raw_file}")
        
        # 保存解析后的配置
        if parsed_data:
            json_file = base_path / "aippt_config_parsed.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, indent=2, ensure_ascii=False)
            print(f"💾 解析配置已保存: {json_file}")
        
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False


def demo_usage():
    """演示如何使用HttpDynamicConfig"""
    print("\n" + "="*60)
    print("📚 HttpDynamicConfig使用示例")
    print("="*60)
    
    # 创建客户端
    config_client = HttpDynamicConfig()
    
    # 示例1: 获取默认配置
    print("\n1️⃣ 获取默认配置:")
    default_config = config_client.get_config()
    if default_config:
        preview = default_config[:100] + "..." if len(default_config) > 100 else default_config
        print(f"   默认配置预览: {preview}")
    
    # 示例2: 获取指定配置
    print("\n2️⃣ 获取指定配置:")
    aippt_config = config_client.get_config(
        data_id="wuying-alpha-service:aippt_config",
        group="DEFAULT_GROUP"
    )
    if aippt_config:
        lines = aippt_config.strip().split('\n')
        print(f"   AIPPT配置: {len(lines)} 行数据")
        print(f"   前3行: {lines[:3]}")
    
    # 示例3: 尝试解析JSON配置
    print("\n3️⃣ 配置解析示例:")
    if aippt_config:
        try:
            # 尝试作为JSON解析
            json_data = json.loads(aippt_config)
            print(f"   JSON格式: {type(json_data)}")
        except:
            print(f"   非JSON格式，按文本处理")
    
    print("\n✅ 使用示例完成")


def main():
    """主函数"""
    # 获取配置
    config = get_aippt_config()
    
    # 演示用法
    if config:
        demo_usage()
    
    return config is not None


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
