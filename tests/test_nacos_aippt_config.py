#!/usr/bin/env python3
"""
测试脚本：获取Nacos中的aippt_config配置
获取 groupId=DEFAULT_GROUP，dataId=wuying-alpha-service:aippt_config 的值
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_get_aippt_config():
    """测试获取aippt_config配置"""
    print("=== 测试获取Nacos中的aippt_config配置 ===")
    
    try:
        # 导入Nacos配置管理器
        from shared.config.nacos_config import NacosConfigManager
        
        # 创建配置管理器实例
        nacos_manager = NacosConfigManager()
        
        # 显示连接信息
        connection_info = nacos_manager.get_connection_info()
        print(f"\n📋 Nacos连接信息:")
        print(f"  - 端点: {connection_info['endpoint']}")
        print(f"  - 默认DataId: {connection_info['default_data_id']}")
        print(f"  - 默认Group: {connection_info['default_group']}")
        print(f"  - 连接状态: {'✅ 已连接' if connection_info['is_connected'] else '❌ 未连接'}")
        
        # 指定要获取的配置参数
        target_data_id = "wuying-alpha-service:aippt_config"
        target_group = "DEFAULT_GROUP"
        
        print(f"\n🎯 目标配置:")
        print(f"  - DataId: {target_data_id}")
        print(f"  - Group: {target_group}")
        
        # 获取指定的配置
        print(f"\n🔍 正在获取配置...")
        config = nacos_manager.get_config(
            data_id=target_data_id,
            group=target_group
        )
        
        if config:
            print(f"✅ 成功获取配置，共 {len(config)} 个配置项")
            print(f"\n📄 配置内容:")
            print("=" * 60)
            
            # 格式化输出配置内容
            if isinstance(config, dict):
                # 如果是字典，格式化输出
                formatted_config = json.dumps(config, indent=2, ensure_ascii=False)
                print(formatted_config)
                
                # 显示主要配置项
                print("\n🔧 主要配置项:")
                for key, value in config.items():
                    if isinstance(value, dict):
                        print(f"  - {key}: {len(value)} 个子项")
                        for sub_key in list(value.keys())[:3]:  # 只显示前3个子项
                            print(f"    └─ {sub_key}: {value[sub_key]}")
                        if len(value) > 3:
                            print(f"    └─ ... 还有 {len(value) - 3} 个子项")
                    else:
                        # 如果值太长，截断显示
                        str_value = str(value)
                        if len(str_value) > 100:
                            str_value = str_value[:100] + "..."
                        print(f"  - {key}: {str_value}")
            else:
                # 如果不是字典，直接输出
                print(config)
            
            print("=" * 60)
            
            # 测试获取特定配置值
            print(f"\n🔍 测试获取特定配置值:")
            test_keys = [
                "app.name",
                "aippt.enabled", 
                "aippt.config",
                "service.version",
                "features"
            ]
            
            for key in test_keys:
                value = nacos_manager.get_config_value(
                    key=key,
                    default="未找到",
                    data_id=target_data_id,
                    group=target_group
                )
                print(f"  - {key}: {value}")
                
        else:
            print("❌ 未获取到配置内容")
            print("可能的原因:")
            print("  1. 配置不存在")
            print("  2. Nacos服务不可用")
            print("  3. 网络连接问题")
            print("  4. 权限不足")
        
        # 测试配置缓存
        print(f"\n💾 配置缓存状态:")
        cached_configs = connection_info.get('cached_configs', [])
        fallback_configs = connection_info.get('fallback_configs', [])
        
        print(f"  - 缓存配置数量: {len(cached_configs)}")
        print(f"  - 降级配置数量: {len(fallback_configs)}")
        
        if cached_configs:
            print("  - 已缓存的配置:")
            for config_key in cached_configs:
                print(f"    └─ {config_key}")
        
        # 测试连接状态
        print(f"\n🔗 连接测试:")
        is_connected = nacos_manager.is_connected()
        print(f"  - 当前连接状态: {'✅ 正常' if is_connected else '❌ 异常'}")
        
        if not is_connected:
            print("  - 建议检查:")
            print("    1. Nacos服务是否正常运行")
            print("    2. 网络连接是否正常")
            print("    3. 配置文件中的endpoint是否正确")
            print("    4. 防火墙设置")
        
        print(f"\n✅ 测试完成")
        return config
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("  pip install nacos-sdk-python")
        return None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_nacos_client_direct():
    """直接使用Nacos客户端测试连接"""
    print("\n=== 直接测试Nacos客户端连接 ===")

    try:
        import nacos
        from shared.config.environments import env_manager

        # 获取环境配置
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint

        print(f"📡 使用端点: {endpoint}")

        # 解析endpoint
        if "://" in endpoint:
            from urllib.parse import urlparse
            parsed = urlparse(endpoint)
            server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
        else:
            server_addresses = endpoint

        print(f"🔗 服务器地址: {server_addresses}")

        # 创建Nacos客户端
        client = nacos.NacosClient(
            server_addresses=server_addresses,
            namespace="",
            username="",
            password=""
        )

        # 首先测试获取默认配置（验证连接）
        default_data_id = config.nacos_data_id
        default_group = config.nacos_group

        print(f"🔍 先测试获取默认配置: {default_group}:{default_data_id}")

        default_config = client.get_config(
            data_id=default_data_id,
            group=default_group,
            timeout=10
        )

        if default_config:
            print(f"✅ 默认配置获取成功 ({len(default_config)} 字符)")
            print("📄 默认配置内容预览:")
            preview = default_config[:200] + "..." if len(default_config) > 200 else default_config
            print(f"  {preview}")
        else:
            print("❌ 默认配置获取失败")

        # 然后测试获取目标配置
        target_data_id = "wuying-alpha-service:aippt_config"
        target_group = "DEFAULT_GROUP"

        print(f"\n🎯 尝试获取目标配置: {target_group}:{target_data_id}")

        config_content = client.get_config(
            data_id=target_data_id,
            group=target_group,
            timeout=10
        )

        if config_content:
            print(f"✅ 成功获取配置内容 ({len(config_content)} 字符)")
            print("📄 原始配置内容:")
            print("-" * 40)
            print(config_content)
            print("-" * 40)

            # 尝试解析配置内容
            try:
                import json
                parsed_config = json.loads(config_content)
                print("✅ 配置内容为有效JSON格式")
                print("📋 解析后的配置:")
                formatted = json.dumps(parsed_config, indent=2, ensure_ascii=False)
                print(formatted)
            except json.JSONDecodeError:
                try:
                    import yaml
                    parsed_config = yaml.safe_load(config_content)
                    print("✅ 配置内容为有效YAML格式")
                    print("📋 解析后的配置:")
                    print(yaml.dump(parsed_config, default_flow_style=False, allow_unicode=True))
                except:
                    print("⚠️ 配置内容为纯文本格式")
        else:
            print("❌ 未获取到目标配置内容")
            print("💡 建议检查:")
            print("  1. 配置是否存在于Nacos中")
            print("  2. DataId和Group是否正确")
            print("  3. 是否有访问权限")

            # 尝试一些可能的配置名称
            print("\n🔍 尝试获取其他可能的配置:")
            possible_configs = [
                ("wuying-alpha-service:aippt", "DEFAULT_GROUP"),
                ("alpha-service:aippt_config", "DEFAULT_GROUP"),
                ("aippt_config", "DEFAULT_GROUP"),
                ("wuying-alpha-service:aippt_config", "AIPPT_GROUP"),
            ]

            for data_id, group in possible_configs:
                try:
                    test_config = client.get_config(
                        data_id=data_id,
                        group=group,
                        timeout=5
                    )
                    if test_config:
                        print(f"  ✅ 找到配置: {group}:{data_id} ({len(test_config)} 字符)")
                    else:
                        print(f"  ❌ 未找到: {group}:{data_id}")
                except Exception as e:
                    print(f"  ❌ 错误: {group}:{data_id} - {e}")

    except Exception as e:
        print(f"❌ 直接连接测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_nacos_server_status():
    """测试Nacos服务器状态"""
    print("\n=== 测试Nacos服务器状态 ===")

    try:
        import requests
        from shared.config.environments import env_manager

        config = env_manager.get_config()
        endpoint = config.nacos_endpoint

        # 解析endpoint获取基础URL
        if "diamond-server/diamond" in endpoint:
            base_url = endpoint.replace("/diamond-server/diamond", "")
        else:
            base_url = endpoint

        print(f"🌐 基础URL: {base_url}")

        # 测试服务器连通性
        try:
            response = requests.get(f"{base_url}/nacos/v1/ns/operator/servers", timeout=10)
            print(f"✅ 服务器连通性: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 服务器连通性测试失败: {e}")

        # 测试配置服务
        try:
            response = requests.get(f"{base_url}/nacos/v1/cs/configs",
                                  params={"pageNo": 1, "pageSize": 10},
                                  timeout=10)
            print(f"✅ 配置服务: HTTP {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"  - 总配置数: {data.get('totalCount', 0)}")
                configs = data.get('pageItems', [])
                if configs:
                    print("  - 前几个配置:")
                    for config in configs[:3]:
                        print(f"    └─ {config.get('group', 'N/A')}:{config.get('dataId', 'N/A')}")
        except Exception as e:
            print(f"❌ 配置服务测试失败: {e}")

    except ImportError:
        print("⚠️ 需要安装requests库: pip install requests")
    except Exception as e:
        print(f"❌ 服务器状态测试失败: {e}")


def create_test_config():
    """创建测试配置（如果有权限的话）"""
    print("\n=== 尝试创建测试配置 ===")

    try:
        import nacos
        from shared.config.environments import env_manager

        config = env_manager.get_config()
        endpoint = config.nacos_endpoint

        # 解析endpoint
        if "://" in endpoint:
            from urllib.parse import urlparse
            parsed = urlparse(endpoint)
            server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
        else:
            server_addresses = endpoint

        # 创建Nacos客户端
        client = nacos.NacosClient(
            server_addresses=server_addresses,
            namespace="",
            username="",
            password=""
        )

        # 测试配置内容
        test_config_content = {
            "aippt": {
                "enabled": True,
                "version": "1.0.0",
                "features": {
                    "auto_generate": True,
                    "template_support": True,
                    "export_formats": ["pptx", "pdf"]
                }
            },
            "service": {
                "name": "alpha-service",
                "version": "1.0.0"
            },
            "created_by": "test_script",
            "created_at": time.strftime('%Y-%m-%d %H:%M:%S')
        }

        config_json = json.dumps(test_config_content, indent=2, ensure_ascii=False)

        # 尝试发布配置
        target_data_id = "wuying-alpha-service:aippt_config"
        target_group = "DEFAULT_GROUP"

        print(f"📝 尝试创建配置: {target_group}:{target_data_id}")

        result = client.publish_config(
            data_id=target_data_id,
            group=target_group,
            content=config_json,
            timeout=10
        )

        if result:
            print("✅ 配置创建成功")
            print("📄 配置内容:")
            print(config_json)

            # 验证配置是否创建成功
            time.sleep(1)  # 等待一下
            retrieved_config = client.get_config(
                data_id=target_data_id,
                group=target_group,
                timeout=5
            )

            if retrieved_config:
                print("✅ 配置验证成功，可以正常获取")
                return True
            else:
                print("❌ 配置验证失败，无法获取刚创建的配置")
                return False
        else:
            print("❌ 配置创建失败")
            return False

    except Exception as e:
        print(f"❌ 创建测试配置失败: {e}")
        print("💡 可能的原因:")
        print("  1. 没有写入权限")
        print("  2. 服务器不支持配置发布")
        print("  3. 网络连接问题")
        return False


def main():
    """主函数"""
    print("🚀 开始测试Nacos配置获取")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 显示环境信息
    try:
        from shared.config.environments import env_manager
        env_info = env_manager.get_environment_info()
        print(f"🌍 当前环境: {env_info['current_environment']}")
    except:
        print("⚠️ 无法获取环境信息")

    # 测试1: 服务器状态
    test_nacos_server_status()

    # 测试2: 使用NacosConfigManager
    config = test_get_aippt_config()

    # 测试3: 直接使用Nacos客户端
    test_nacos_client_direct()

    # 测试4: 如果配置不存在，尝试创建
    if not config:
        print("\n💡 配置不存在，尝试创建测试配置...")
        created = create_test_config()
        if created:
            print("✅ 测试配置创建成功，重新获取...")
            config = test_get_aippt_config()

    print(f"\n🏁 所有测试完成")

    if config:
        print("✅ 成功获取到配置")
        return True
    else:
        print("❌ 未能获取到配置")
        print("\n📋 总结:")
        print("  1. 检查Nacos服务器是否正常运行")
        print("  2. 确认配置 'wuying-alpha-service:aippt_config' 是否存在")
        print("  3. 检查网络连接和权限设置")
        print("  4. 可以尝试在Nacos控制台手动创建配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
