#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nacos配置中心测试
"""
import sys
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_nacos_config_manager():
    """测试Nacos配置管理器"""
    print("=== 测试Nacos配置管理器 ===")
    
    try:
        from shared.config.nacos_config import nacos_config_manager
        
        # 1. 测试连接信息
        print("\n1. 连接信息测试:")
        info = nacos_config_manager.get_connection_info()
        print(f"连接信息: {json.dumps(info, indent=2, ensure_ascii=False)}")
        
        # 2. 测试连接状态
        print(f"\n2. 连接状态: {'已连接' if nacos_config_manager.is_connected() else '未连接'}")
        
        # 3. 测试获取配置
        print("\n3. 获取配置测试:")
        config = nacos_config_manager.get_config()
        print(f"配置内容: {json.dumps(config, indent=2, ensure_ascii=False)}")
        
        # 4. 测试获取特定配置值
        print("\n4. 获取特定配置值测试:")
        test_keys = [
            ("database.host", "localhost"),
            ("redis.port", 6379),
            ("app.name", "alpha-service"),
            ("non.existent.key", "default_value")
        ]
        
        for key, default in test_keys:
            value = nacos_config_manager.get_config_value(key, default)
            print(f"  {key}: {value}")
        
        # 5. 测试设置降级配置
        print("\n5. 设置降级配置测试:")
        fallback_config = {
            "database": {
                "host": "fallback-db",
                "port": 3306,
                "name": "alpha_service"
            },
            "redis": {
                "host": "fallback-redis",
                "port": 6379
            },
            "app": {
                "name": "alpha-service-fallback",
                "version": "1.0.0"
            }
        }
        
        nacos_config_manager.set_fallback_config(fallback_config)
        print("降级配置设置完成")
        
        # 6. 测试配置监听器
        print("\n6. 配置监听器测试:")
        
        config_changes = []
        
        def test_callback(new_config):
            config_changes.append(new_config)
            print(f"配置变更回调触发: {len(new_config)} 个配置项")
        
        nacos_config_manager.add_config_listener(test_callback)
        print("配置监听器已添加")
        
        # 7. 测试手动刷新配置
        print("\n7. 手动刷新配置测试:")
        refresh_success = nacos_config_manager.refresh_config()
        print(f"配置刷新结果: {'成功' if refresh_success else '失败'}")
        
        # 等待一下看是否有配置变更回调
        time.sleep(1)
        if config_changes:
            print(f"检测到 {len(config_changes)} 次配置变更")
        
        # 8. 测试多个配置源
        print("\n8. 多配置源测试:")
        try:
            # 测试获取不存在的配置源
            custom_config = nacos_config_manager.get_config(
                data_id="test:config",
                group="TEST_GROUP"
            )
            print(f"自定义配置: {custom_config}")
        except Exception as e:
            print(f"获取自定义配置失败（预期行为）: {e}")
        
        print("\n✅ Nacos配置管理器测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_config_parsing():
    """测试配置解析功能"""
    print("\n=== 测试配置解析功能 ===")
    
    try:
        from shared.config.nacos_config import NacosConfigManager
        
        manager = NacosConfigManager()
        
        # 测试JSON解析
        json_content = '{"database": {"host": "localhost", "port": 3306}, "app": {"name": "test"}}'
        json_config = manager._parse_config_content(json_content)
        print(f"JSON解析结果: {json_config}")
        
        # 测试YAML解析
        yaml_content = """
database:
  host: localhost
  port: 3306
app:
  name: test
  features:
    - feature1
    - feature2
"""
        yaml_config = manager._parse_config_content(yaml_content)
        print(f"YAML解析结果: {yaml_config}")
        
        # 测试无效内容
        invalid_content = "invalid content"
        invalid_config = manager._parse_config_content(invalid_content)
        print(f"无效内容解析结果: {invalid_config}")
        
        print("✅ 配置解析测试完成")
        
    except Exception as e:
        print(f"❌ 配置解析测试失败: {e}")


def test_thread_safety():
    """测试线程安全性"""
    print("\n=== 测试线程安全性 ===")
    
    try:
        import threading
        from shared.config.nacos_config import nacos_config_manager
        
        results = []
        errors = []
        
        def worker(worker_id):
            try:
                for i in range(10):
                    # 并发获取配置
                    config = nacos_config_manager.get_config()
                    value = nacos_config_manager.get_config_value("test.key", f"default-{worker_id}-{i}")
                    results.append((worker_id, i, len(config), value))
                    time.sleep(0.01)  # 短暂延迟
            except Exception as e:
                errors.append((worker_id, str(e)))
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print(f"线程安全测试完成:")
        print(f"  成功操作: {len(results)} 次")
        print(f"  错误次数: {len(errors)} 次")
        
        if errors:
            print("错误详情:")
            for worker_id, error in errors:
                print(f"  Worker {worker_id}: {error}")
        
        print("✅ 线程安全测试完成")
        
    except Exception as e:
        print(f"❌ 线程安全测试失败: {e}")


def test_singleton_pattern():
    """测试单例模式"""
    print("\n=== 测试单例模式 ===")
    
    try:
        from shared.config.nacos_config import NacosConfigManager, nacos_config_manager
        
        # 创建多个实例
        instance1 = NacosConfigManager()
        instance2 = NacosConfigManager()
        
        # 检查是否为同一个实例
        print(f"instance1 is instance2: {instance1 is instance2}")
        print(f"instance1 is nacos_config_manager: {instance1 is nacos_config_manager}")
        print(f"instance2 is nacos_config_manager: {instance2 is nacos_config_manager}")
        
        # 检查ID
        print(f"instance1 id: {id(instance1)}")
        print(f"instance2 id: {id(instance2)}")
        print(f"nacos_config_manager id: {id(nacos_config_manager)}")
        
        if instance1 is instance2 and instance1 is nacos_config_manager:
            print("✅ 单例模式测试通过")
        else:
            print("❌ 单例模式测试失败")
        
    except Exception as e:
        print(f"❌ 单例模式测试失败: {e}")


if __name__ == "__main__":
    print("开始Nacos配置中心测试...")
    
    # 运行所有测试
    test_singleton_pattern()
    test_config_parsing()
    test_nacos_config_manager()
    test_thread_safety()
    
    print("\n🎉 所有测试完成！")
