#!/usr/bin/env python3
"""
获取Nacos中的aippt_config配置
groupId=DEFAULT_GROUP，dataId=wuying-alpha-service:aippt_config
"""

import sys
import os
import json
import time
import requests
from pathlib import Path
from urllib.parse import urlencode

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def get_aippt_config():
    """获取AIPPT配置"""
    try:
        from shared.config.environments import env_manager

        # 获取环境配置
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint


        # 目标配置参数
        target_data_id = "wuying-alpha-service:aippt_config"
        target_group = "DEFAULT_GROUP"

        # 构建请求URL
        params = {
            'dataId': target_data_id,
            'group': target_group
        }

        config_url = f"{endpoint}?{urlencode(params)}"

        # 发送请求
        headers = {
            'User-Agent': 'Diamond-Client/1.0',
            'Accept': 'text/plain,application/json,*/*'
        }

        print(f"🔍 正在获取配置: {target_group}:{target_data_id}")
        print(f"🔗 请求URL: {config_url}")

        response = requests.get(config_url, headers=headers, timeout=15)

        if response.status_code == 200:
            config_content = response.text
            print(f"✅ 成功获取配置 ({len(config_content)} 字符)")
            print(f"✅ 成功获取配置 ({config_content} )")

            if config_content.strip():
                return config_content
            else:
                print("❌ 配置内容为空")
                return None
        else:
            print(f"❌ 获取配置失败: HTTP {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ 获取配置异常: {e}")
        return None


def parse_aippt_config(config_content):
    """解析AIPPT配置内容"""
    if not config_content:
        return None
    
    # 按行分割IP地址
    ip_list = [line.strip() for line in config_content.strip().split('\n') if line.strip()]
    
    print(f"📋 配置解析结果:")
    print(f"  - 配置类型: IP地址列表")
    print(f"  - IP地址数量: {len(ip_list)}")
    print(f"  - 前10个IP地址:")
    
    for i, ip in enumerate(ip_list[:10]):
        print(f"    {i+1:2d}. {ip}")
    
    if len(ip_list) > 10:
        print(f"    ... 还有 {len(ip_list) - 10} 个IP地址")
    
    return {
        "type": "ip_list",
        "count": len(ip_list),
        "ips": ip_list,
        "raw_content": config_content
    }


def save_config_to_file(config_data, filename="aippt_config.json"):
    """保存配置到文件"""
    try:
        output_file = Path(__file__).parent / filename
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 配置已保存到: {output_file}")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 获取Nacos AIPPT配置")
    print(f"📅 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示环境信息
    try:
        from shared.config.environments import env_manager
        env_info = env_manager.get_environment_info()
        print(f"🌍 环境: {env_info['current_environment']}")
    except:
        print("⚠️ 无法获取环境信息")
    
    print("-" * 50)
    
    # 获取配置
    config_content = get_aippt_config()
    
    if config_content:
        print("-" * 50)
        
        # 解析配置
        config_data = parse_aippt_config(config_content)
        
        if config_data:
            print("-" * 50)
            
            # 保存配置
            save_config_to_file(config_data)
            
            print("-" * 50)
            print("✅ 配置获取完成")
            
            # 显示配置摘要
            print(f"\n📊 配置摘要:")
            print(f"  - 配置名称: wuying-alpha-service:aippt_config")
            print(f"  - 配置组: DEFAULT_GROUP")
            print(f"  - 配置类型: IP地址列表")
            print(f"  - IP数量: {config_data['count']}")
            print(f"  - 内容长度: {len(config_content)} 字符")
            
            return True
        else:
            print("❌ 配置解析失败")
            return False
    else:
        print("❌ 未能获取到配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
